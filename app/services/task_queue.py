"""
Background task processing with Celery.
"""

import asyncio
from datetime import datetime
from typing import Any, Dict, List, Optional

from celery import Celery
from celery.result import AsyncResult

from app.config.settings import settings
from app.models.scraping_job import JobStatus, ScrapingJob
from app.services.data_processor import DataCleaningConfig, DataProcessingService
from app.services.firebase_service import firebase_service
from app.services.scraper import ExtractionConfig, ScrapingConfig, WebScrapingService
from app.utils.logger import get_logger

logger = get_logger(__name__)

# Create Celery app
celery_app = Celery(
    "crawl_agent",
    broker=settings.celery_broker_url,
    backend=settings.celery_result_backend,
    include=["app.services.task_queue"],
)

# Configure Celery
celery_app.conf.update(
    task_serializer=settings.celery_task_serializer,
    result_serializer=settings.celery_result_serializer,
    accept_content=settings.celery_accept_content,
    timezone=settings.celery_timezone,
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
)


@celery_app.task(bind=True, name="scrape_job")
def scrape_job_task(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Execute a scraping job as a background task.
    
    Args:
        job_data: Serialized scraping job data
        
    Returns:
        Task result with job status and results
    """
    job_id = job_data.get("job_id")
    logger.info(f"Starting scraping job task: {job_id}")
    
    try:
        # Update task status
        self.update_state(
            state="PROGRESS",
            meta={"status": "Starting job execution", "progress": 0}
        )
        
        # Deserialize job
        job = ScrapingJob(**job_data)
        
        # Update job status in database
        asyncio.run(firebase_service.update_scraping_job(
            job_id, 
            {
                "status": JobStatus.RUNNING.value,
                "started_at": datetime.utcnow(),
                "updated_at": datetime.utcnow(),
            }
        ))
        
        # Execute scraping
        result = asyncio.run(execute_scraping_job_async(job, self))
        
        logger.info(f"Scraping job completed: {job_id}")
        return result
        
    except Exception as e:
        logger.error(f"Scraping job failed: {job_id}, error: {e}")
        
        # Update job status to failed
        if job_id:
            asyncio.run(firebase_service.update_scraping_job(
                job_id,
                {
                    "status": JobStatus.FAILED.value,
                    "error_message": str(e),
                    "completed_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow(),
                }
            ))
        
        # Re-raise exception for Celery
        raise


async def execute_scraping_job_async(job: ScrapingJob, task=None) -> Dict[str, Any]:
    """
    Execute scraping job asynchronously.
    
    Args:
        job: Scraping job to execute
        task: Celery task instance for progress updates
        
    Returns:
        Job execution result
    """
    try:
        # Create scraping service
        async with WebScrapingService() as scraper:
            # Convert job config to scraping configs
            urls = [str(url) for url in job.config.urls]
            total_urls = len(urls)
            
            scraping_config = ScrapingConfig(
                url=urls[0],  # Will be overridden for each URL
                max_depth=job.config.max_depth,
                delay=job.config.delay,
                timeout=job.config.timeout,
                user_agent=job.config.user_agent,
                headers=job.config.headers,
                cookies=job.config.cookies,
                proxy=job.config.proxy,
                javascript=job.config.javascript,
                wait_for=job.config.wait_for,
                screenshot=job.config.screenshot,
                pdf=job.config.pdf,
            )
            
            extraction_config = ExtractionConfig(
                strategy=job.config.extraction_strategy,
                selectors=job.config.css_selectors,
                schema=job.config.json_schema,
                llm_prompt=job.config.llm_prompt,
                similarity_threshold=job.config.similarity_threshold,
            )
            
            # Update progress
            if task:
                task.update_state(
                    state="PROGRESS",
                    meta={"status": "Scraping URLs", "progress": 10}
                )
            
            # Execute scraping
            results = await scraper.scrape_multiple_urls(
                urls=urls,
                config=scraping_config,
                extraction_config=extraction_config,
                max_concurrent=5,
            )
            
            # Update progress
            if task:
                task.update_state(
                    state="PROGRESS",
                    meta={"status": "Processing data", "progress": 70}
                )
            
            # Process results if data cleaning is enabled
            if job.config.clean_data:
                data_processor = DataProcessingService()
                cleaning_config = DataCleaningConfig(
                    remove_html=job.config.remove_html,
                    normalize_whitespace=job.config.normalize_whitespace,
                    min_text_length=job.config.min_text_length,
                    max_text_length=job.config.max_text_length,
                )
                
                # Clean each result
                for result in results:
                    if result.content:
                        cleaned_data = data_processor.clean_data(
                            {"content": result.content}, 
                            cleaning_config
                        )
                        result.content = cleaned_data.get("content", result.content)
            
            # Calculate statistics
            successful_results = [r for r in results if not r.error]
            failed_results = [r for r in results if r.error]
            
            # Update job with results
            job_updates = {
                "status": JobStatus.COMPLETED.value,
                "results": [r.dict() for r in results],
                "stats": {
                    "total_urls": total_urls,
                    "processed_urls": len(results),
                    "successful_urls": len(successful_results),
                    "failed_urls": len(failed_results),
                    "total_pages_scraped": len(successful_results),
                    "average_processing_time": sum(r.duration for r in results) / len(results) if results else 0,
                    "start_time": job.started_at.isoformat() if job.started_at else None,
                    "end_time": datetime.utcnow().isoformat(),
                    "total_duration": (datetime.utcnow() - job.started_at).total_seconds() if job.started_at else 0,
                },
                "completed_at": datetime.utcnow(),
                "updated_at": datetime.utcnow(),
            }
            
            # Update progress
            if task:
                task.update_state(
                    state="PROGRESS",
                    meta={"status": "Saving results", "progress": 90}
                )
            
            # Save results to database
            await firebase_service.update_scraping_job(job.job_id, job_updates)
            
            # Update progress
            if task:
                task.update_state(
                    state="SUCCESS",
                    meta={"status": "Job completed", "progress": 100}
                )
            
            return {
                "job_id": job.job_id,
                "status": "completed",
                "total_urls": total_urls,
                "successful_urls": len(successful_results),
                "failed_urls": len(failed_results),
                "duration": job_updates["stats"]["total_duration"],
            }
    
    except Exception as e:
        logger.error(f"Job execution failed: {job.job_id}, error: {e}")
        
        # Update job status to failed
        await firebase_service.update_scraping_job(
            job.job_id,
            {
                "status": JobStatus.FAILED.value,
                "error_message": str(e),
                "completed_at": datetime.utcnow(),
                "updated_at": datetime.utcnow(),
            }
        )
        
        raise


@celery_app.task(name="cleanup_old_jobs")
def cleanup_old_jobs_task(days_old: int = 30) -> Dict[str, Any]:
    """
    Clean up old completed jobs.
    
    Args:
        days_old: Number of days after which to clean up jobs
        
    Returns:
        Cleanup statistics
    """
    logger.info(f"Starting cleanup of jobs older than {days_old} days")
    
    try:
        # TODO: Implement job cleanup logic
        # 1. Find jobs older than specified days
        # 2. Delete job data and associated files
        # 3. Update statistics
        
        return {
            "status": "completed",
            "jobs_cleaned": 0,
            "storage_freed_mb": 0.0,
        }
        
    except Exception as e:
        logger.error(f"Job cleanup failed: {e}")
        raise


@celery_app.task(name="update_usage_stats")
def update_usage_stats_task(user_id: str, period: str = "monthly") -> Dict[str, Any]:
    """
    Update user usage statistics.
    
    Args:
        user_id: User ID to update stats for
        period: Statistics period (daily, weekly, monthly)
        
    Returns:
        Update result
    """
    logger.info(f"Updating usage stats for user {user_id}, period: {period}")
    
    try:
        # TODO: Implement usage statistics update
        # 1. Calculate usage for the period
        # 2. Update user quota and statistics
        # 3. Send notifications if limits are reached
        
        return {
            "status": "completed",
            "user_id": user_id,
            "period": period,
        }
        
    except Exception as e:
        logger.error(f"Usage stats update failed for user {user_id}: {e}")
        raise


class TaskQueueService:
    """Service for managing background tasks."""
    
    @staticmethod
    def submit_scraping_job(job: ScrapingJob) -> str:
        """
        Submit a scraping job to the task queue.
        
        Args:
            job: Scraping job to submit
            
        Returns:
            Task ID
        """
        logger.info(f"Submitting scraping job to queue: {job.job_id}")
        
        # Convert job to dict for serialization
        job_data = job.dict()
        
        # Submit task
        task = scrape_job_task.delay(job_data)
        
        logger.info(f"Scraping job submitted with task ID: {task.id}")
        return task.id
    
    @staticmethod
    def get_task_status(task_id: str) -> Dict[str, Any]:
        """
        Get task status and progress.
        
        Args:
            task_id: Task ID to check
            
        Returns:
            Task status information
        """
        result = AsyncResult(task_id, app=celery_app)
        
        return {
            "task_id": task_id,
            "status": result.status,
            "result": result.result,
            "info": result.info,
        }
    
    @staticmethod
    def cancel_task(task_id: str) -> bool:
        """
        Cancel a running task.
        
        Args:
            task_id: Task ID to cancel
            
        Returns:
            True if task was cancelled successfully
        """
        try:
            celery_app.control.revoke(task_id, terminate=True)
            logger.info(f"Task cancelled: {task_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to cancel task {task_id}: {e}")
            return False
    
    @staticmethod
    def schedule_cleanup(days_old: int = 30) -> str:
        """
        Schedule cleanup of old jobs.
        
        Args:
            days_old: Number of days after which to clean up jobs
            
        Returns:
            Task ID
        """
        task = cleanup_old_jobs_task.delay(days_old)
        logger.info(f"Cleanup task scheduled with ID: {task.id}")
        return task.id
    
    @staticmethod
    def schedule_usage_update(user_id: str, period: str = "monthly") -> str:
        """
        Schedule usage statistics update.
        
        Args:
            user_id: User ID to update stats for
            period: Statistics period
            
        Returns:
            Task ID
        """
        task = update_usage_stats_task.delay(user_id, period)
        logger.info(f"Usage stats update scheduled with ID: {task.id}")
        return task.id


# Global task queue service instance
task_queue_service = TaskQueueService()

# Export celery app for Celery CLI
app = celery_app
