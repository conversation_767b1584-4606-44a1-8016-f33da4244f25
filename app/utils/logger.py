"""
Logging configuration and utilities.
"""

import logging
import logging.config
import os
import sys
from pathlib import Path
from typing import Any, Dict

import structlog
from structlog.stdlib import LoggerFactory

from app.config.settings import settings


def setup_logging() -> None:
    """Setup application logging configuration."""

    # Ensure logs directory exists and is writable
    try:
        log_dir = Path(settings.log_file).parent
        log_dir.mkdir(parents=True, exist_ok=True)

        # Test if we can write to the log file
        test_file = log_dir / "test_write.log"
        test_file.touch()
        test_file.unlink()
        use_file_handler = True
    except (OSError, PermissionError) as e:
        print(f"Warning: Cannot write to log directory {log_dir}: {e}")
        print("Falling back to console-only logging")
        use_file_handler = False
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer() if settings.log_format == "json" 
            else structlog.dev.ConsoleRenderer(),
        ],
        context_class=dict,
        logger_factory=LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Standard logging configuration
    logging_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "json": {
                "()": structlog.stdlib.ProcessorFormatter,
                "processor": structlog.processors.JSONRenderer(),
            },
            "console": {
                "()": structlog.stdlib.ProcessorFormatter,
                "processor": structlog.dev.ConsoleRenderer(colors=True),
            },
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": settings.log_level,
                "formatter": "console" if settings.debug else "json",
                "stream": sys.stdout,
            },
        },
    }

    # Add file handler if available
    if use_file_handler:
        logging_config["handlers"]["file"] = {
            "class": "logging.handlers.RotatingFileHandler",
            "level": settings.log_level,
            "formatter": "json",
            "filename": settings.log_file,
            "maxBytes": 10485760,  # 10MB
            "backupCount": 5,
        }

    # Configure loggers with appropriate handlers
    handlers = ["console", "file"] if use_file_handler else ["console"]

    logging_config["loggers"] = {
        "": {
            "handlers": handlers,
            "level": settings.log_level,
            "propagate": False,
        },
        "uvicorn": {
            "handlers": handlers,
            "level": "INFO",
            "propagate": False,
        },
        "uvicorn.error": {
            "handlers": handlers,
            "level": "INFO",
            "propagate": False,
        },
        "uvicorn.access": {
            "handlers": handlers,
            "level": "INFO",
            "propagate": False,
        },
        "fastapi": {
            "handlers": handlers,
            "level": "INFO",
            "propagate": False,
        },
        "firebase_admin": {
            "handlers": handlers,
            "level": "WARNING",
            "propagate": False,
        },
        "google": {
            "handlers": handlers,
            "level": "WARNING",
            "propagate": False,
        },
        "crawl4ai": {
            "handlers": handlers,
            "level": "INFO",
            "propagate": False,
        },
    }
    
    logging.config.dictConfig(logging_config)


def get_logger(name: str) -> structlog.stdlib.BoundLogger:
    """Get a structured logger instance."""
    return structlog.get_logger(name)


class LoggerMixin:
    """Mixin class to add logging capabilities to any class."""
    
    @property
    def logger(self) -> structlog.stdlib.BoundLogger:
        """Get logger for this class."""
        return get_logger(self.__class__.__name__)


def log_function_call(func_name: str, **kwargs) -> None:
    """Log function call with parameters."""
    logger = get_logger("function_call")
    logger.info(f"Calling {func_name}", **kwargs)


def log_error(error: Exception, context: Dict[str, Any] = None) -> None:
    """Log error with context."""
    logger = get_logger("error")
    logger.error(
        f"Error occurred: {type(error).__name__}",
        error_message=str(error),
        context=context or {},
        exc_info=True,
    )


def log_performance(operation: str, duration: float, **kwargs) -> None:
    """Log performance metrics."""
    logger = get_logger("performance")
    logger.info(
        f"Performance: {operation}",
        duration_seconds=duration,
        **kwargs,
    )
